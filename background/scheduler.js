// Scheduler for automated crawling tasks

export class CrawlScheduler {
  constructor(apiClient) {
    this.apiClient = apiClient;
  }

  async setupScheduleAlarm(schedule) {
    const alarmName = `crawl_schedule_${schedule.id}`;
    
    // Clear existing alarm if any
    await chrome.alarms.clear(alarmName);
    
    if (schedule.isActive) {
      // Calculate when to run next (in minutes from now)
      const delayInMinutes = schedule.frequencyMinutes;
      
      await chrome.alarms.create(alarmName, {
        delayInMinutes: delayInMinutes,
        periodInMinutes: delayInMinutes
      });
      
      console.log(`Scheduled alarm for ${alarmName} every ${delayInMinutes} minutes`);
    }
  }

  async executeCrawlSchedule(scheduleId) {
    try {
      console.log(`Executing crawl schedule: ${scheduleId}`);
      
      // Get schedule details from backend
      const scheduleResult = await this.apiClient.makeRequest(`/crawl-schedules/${scheduleId}`);
      
      if (!scheduleResult.success) {
        console.error('Failed to get schedule details:', scheduleResult.error);
        return;
      }
      
      const schedule = scheduleResult.data;
      
      if (!schedule.isActive) {
        console.log('Schedule is not active, skipping execution');
        return;
      }
      
      // Execute the crawl
      await this.performAutomatedCrawl(schedule);
      
    } catch (error) {
      console.error('Error executing crawl schedule:', error);
    }
  }

  async performAutomatedCrawl(schedule) {
    console.log(`Starting automated crawl for: ${schedule.name}`);
    
    const { marketplace, keywords, maxProductsPerRun } = schedule;
    const keywordList = keywords.split(',').map(k => k.trim());
    
    let totalProductsFound = 0;
    
    try {
      for (const keyword of keywordList) {
        if (totalProductsFound >= maxProductsPerRun) {
          break;
        }
        
        const remainingSlots = maxProductsPerRun - totalProductsFound;
        const productsFound = await this.crawlKeyword(marketplace, keyword, remainingSlots);
        totalProductsFound += productsFound;
        
        // Add delay between keywords to avoid rate limiting
        await this.delay(2000 + Math.random() * 3000);
      }
      
      // Update schedule status
      await this.updateScheduleStatus(schedule.id, 'completed', totalProductsFound);
      
      console.log(`Completed crawl for schedule ${schedule.id}. Found ${totalProductsFound} products.`);
      
    } catch (error) {
      console.error('Error during automated crawl:', error);
      await this.updateScheduleStatus(schedule.id, 'failed', totalProductsFound, error.message);
    }
  }

  async crawlKeyword(marketplace, keyword, maxProducts) {
    console.log(`Crawling ${marketplace} for keyword: ${keyword} (max ${maxProducts} products)`);
    
    try {
      // Open a new tab for crawling
      const searchUrl = this.buildSearchUrl(marketplace, keyword);
      const tab = await chrome.tabs.create({ url: searchUrl, active: false });
      
      // Wait for page to load
      await this.waitForTabLoad(tab.id);
      
      // Execute search results extraction
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: this.extractSearchResults,
        args: [marketplace, maxProducts]
      });
      
      const productUrls = results[0].result || [];
      console.log(`Found ${productUrls.length} product URLs for keyword: ${keyword}`);
      
      // Visit each product page and extract data
      let productsExtracted = 0;
      
      for (const productUrl of productUrls.slice(0, maxProducts)) {
        try {
          await chrome.tabs.update(tab.id, { url: productUrl });
          await this.waitForTabLoad(tab.id);
          
          // Extract product data
          const productResults = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: this.extractProductData,
            args: [marketplace]
          });
          
          const productData = productResults[0].result;
          
          if (productData) {
            // Send to backend
            const saveResult = await this.apiClient.createCrawledProduct(productData);
            
            if (saveResult.success) {
              productsExtracted++;
              console.log(`Extracted product: ${productData.title}`);
            }
          }
          
          // Add delay between product pages
          await this.delay(1000 + Math.random() * 2000);
          
        } catch (error) {
          console.error('Error extracting product:', error);
        }
      }
      
      // Close the tab
      await chrome.tabs.remove(tab.id);
      
      return productsExtracted;
      
    } catch (error) {
      console.error('Error crawling keyword:', error);
      return 0;
    }
  }

  buildSearchUrl(marketplace, keyword) {
    const encodedKeyword = encodeURIComponent(keyword);
    
    switch (marketplace) {
      case 'etsy':
        return `https://www.etsy.com/search?q=${encodedKeyword}`;
      case 'ebay':
        return `https://www.ebay.com/sch/i.html?_nkw=${encodedKeyword}`;
      case 'amazon':
        return `https://www.amazon.com/s?k=${encodedKeyword}`;
      default:
        throw new Error(`Unsupported marketplace: ${marketplace}`);
    }
  }

  async waitForTabLoad(tabId) {
    return new Promise((resolve) => {
      const checkTab = () => {
        chrome.tabs.get(tabId, (tab) => {
          if (tab.status === 'complete') {
            resolve();
          } else {
            setTimeout(checkTab, 500);
          }
        });
      };
      checkTab();
    });
  }

  // Function to be injected into search pages
  extractSearchResults(marketplace, maxProducts) {
    const productUrls = [];
    let selectors;
    
    switch (marketplace) {
      case 'etsy':
        selectors = 'a[href*="/listing/"]';
        break;
      case 'ebay':
        selectors = 'a[href*="/itm/"]';
        break;
      case 'amazon':
        selectors = 'a[href*="/dp/"], a[href*="/gp/product/"]';
        break;
      default:
        return [];
    }
    
    const links = document.querySelectorAll(selectors);
    
    for (let i = 0; i < Math.min(links.length, maxProducts); i++) {
      const href = links[i].href;
      if (href && !productUrls.includes(href)) {
        productUrls.push(href);
      }
    }
    
    return productUrls;
  }

  // Function to be injected into product pages
  extractProductData(marketplace) {
    // This will use the same extraction logic as the content scripts
    // Import the appropriate extractor based on marketplace
    
    let extractor;
    switch (marketplace) {
      case 'etsy':
        extractor = window.EtsyExtractor;
        break;
      case 'ebay':
        extractor = window.EbayExtractor;
        break;
      case 'amazon':
        extractor = window.AmazonExtractor;
        break;
      default:
        return null;
    }
    
    if (extractor && extractor.extractProductData) {
      return extractor.extractProductData();
    }
    
    return null;
  }

  async updateScheduleStatus(scheduleId, status, productCount, error = null) {
    try {
      await this.apiClient.makeRequest(`/crawl-schedules/${scheduleId}/status`, {
        method: 'PATCH',
        body: JSON.stringify({
          status,
          productCount,
          error
        })
      });
    } catch (err) {
      console.error('Failed to update schedule status:', err);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
