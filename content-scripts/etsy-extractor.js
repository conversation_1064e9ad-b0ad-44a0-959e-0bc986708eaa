// Etsy-specific product data extractor

window.EtsyExtractor = {
  // Etsy-specific selectors
  selectors: {
    title: [
      'h1[data-test-id="listing-page-title"]',
      'h1.listing-page-title',
      'h1[data-testid="listing-page-title"]'
    ],
    images: [
      'img[data-testid="listing-page-image"]',
      'img[data-test-id="listing-page-image"]',
      '.listing-page-image img',
      '.carousel-image img',
      '.listing-image img'
    ],
    seller: [
      'a[data-testid="shop-name-link"]',
      'a[data-test-id="shop-name-link"]',
      '.shop-name a',
      '.shop-info a'
    ],
    price: [
      '.currency-value',
      '.currency-symbol + .currency-value',
      '[data-testid="price"] .currency-value',
      '.notranslate'
    ],
    rating: [
      '[data-testid="reviews-summary"] [data-testid="rating"]',
      '.shop-rating .rating',
      '.stars-rating'
    ],
    reviewCount: [
      '[data-testid="reviews-summary"] a',
      '.review-count',
      '.reviews-count'
    ],
    description: [
      '[data-testid="description-text"]',
      '.listing-page-description',
      '.description-text'
    ],
    availability: [
      '[data-testid="quantity-select"]',
      '.quantity-select',
      '.inventory-quantity'
    ]
  },

  // Extract product data from Etsy listing page
  extractProductData() {
    try {
      console.log('Extracting Etsy product data...');

      const productData = {
        title: this.extractTitle(),
        productUrl: CommonExtractor.getCurrentUrl(),
        marketplace: 'etsy',
        sellerName: this.extractSeller(),
        images: this.extractImages(),
        metadata: this.extractMetadata()
      };

      // Validate the extracted data
      const validation = CommonExtractor.validateProductData(productData);
      
      if (!validation.isValid) {
        console.error('Etsy product data validation failed:', validation.errors);
        CommonExtractor.showNotification(
          'Failed to extract complete product data: ' + validation.errors.join(', '),
          'error'
        );
        return null;
      }

      console.log('Etsy product data extracted successfully:', productData);
      return productData;

    } catch (error) {
      console.error('Error extracting Etsy product data:', error);
      CommonExtractor.showNotification('Error extracting product data: ' + error.message, 'error');
      return null;
    }
  },

  extractTitle() {
    for (const selector of this.selectors.title) {
      const title = CommonExtractor.extractText(selector);
      if (title) {
        return CommonExtractor.cleanText(title);
      }
    }
    
    // Fallback to page title
    const pageTitle = document.title;
    if (pageTitle && !pageTitle.includes('Etsy')) {
      return CommonExtractor.cleanText(pageTitle.split('|')[0]);
    }
    
    throw new Error('Could not extract product title');
  },

  extractSeller() {
    for (const selector of this.selectors.seller) {
      const seller = CommonExtractor.extractText(selector);
      if (seller) {
        return CommonExtractor.cleanText(seller);
      }
    }
    
    return null;
  },

  extractImages() {
    const images = CommonExtractor.extractImages(this.selectors.images);
    
    // Etsy-specific image processing
    return images.map((image, index) => {
      let imageUrl = image.imageUrl;
      
      // Convert Etsy thumbnail URLs to full size
      if (imageUrl.includes('il_170x135')) {
        imageUrl = imageUrl.replace('il_170x135', 'il_fullxfull');
      } else if (imageUrl.includes('il_340x270')) {
        imageUrl = imageUrl.replace('il_340x270', 'il_fullxfull');
      }
      
      return {
        ...image,
        imageUrl: imageUrl,
        isPrimary: index === 0
      };
    });
  },

  extractMetadata() {
    const metadata = {};
    
    // Extract price
    const price = this.extractPrice();
    if (price) {
      metadata.price = price.raw;
      metadata.currency = price.currency;
    }
    
    // Extract rating
    const rating = this.extractRating();
    if (rating) {
      metadata.rating = rating;
    }
    
    // Extract review count
    const reviewCount = this.extractReviewCount();
    if (reviewCount) {
      metadata.reviewCount = reviewCount;
    }
    
    // Extract description
    const description = this.extractDescription();
    if (description) {
      metadata.description = description;
    }
    
    // Extract availability
    const availability = this.extractAvailability();
    if (availability) {
      metadata.availability = availability;
    }
    
    return metadata;
  },

  extractPrice() {
    return CommonExtractor.extractPrice(this.selectors.price);
  },

  extractRating() {
    for (const selector of this.selectors.rating) {
      const rating = CommonExtractor.extractRating([selector]);
      if (rating) {
        return rating;
      }
    }
    
    return null;
  },

  extractReviewCount() {
    for (const selector of this.selectors.reviewCount) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const text = element.textContent.trim();
          const match = text.match(/(\d+(?:,\d+)*)/);
          if (match) {
            return match[1];
          }
        }
      } catch (error) {
        console.warn(`Failed to extract review count from ${selector}:`, error);
      }
    }
    
    return null;
  },

  extractDescription() {
    for (const selector of this.selectors.description) {
      const description = CommonExtractor.extractText(selector);
      if (description && description.length > 10) {
        return CommonExtractor.cleanText(description).substring(0, 500);
      }
    }
    
    return null;
  },

  extractAvailability() {
    for (const selector of this.selectors.availability) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          // Check if quantity selector exists and is not disabled
          if (element.tagName === 'SELECT' && !element.disabled) {
            return 'In Stock';
          }
          
          const text = element.textContent.trim().toLowerCase();
          if (text.includes('in stock') || text.includes('available')) {
            return 'In Stock';
          } else if (text.includes('out of stock') || text.includes('sold out')) {
            return 'Out of Stock';
          }
        }
      } catch (error) {
        console.warn(`Failed to extract availability from ${selector}:`, error);
      }
    }
    
    return 'Unknown';
  },

  // Check if current page is an Etsy product page
  isProductPage() {
    return window.location.pathname.includes('/listing/') && 
           window.location.hostname.includes('etsy.com');
  }
};

// Auto-extract when page loads (for manual crawling)
if (EtsyExtractor.isProductPage()) {
  // Wait for page to fully load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        console.log('Etsy product page detected and ready for extraction');
      }, 1000);
    });
  } else {
    console.log('Etsy product page detected and ready for extraction');
  }
}
