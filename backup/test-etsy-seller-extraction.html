<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Etsy Seller Extraction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Etsy Seller Extraction Test</h1>
    
    <div class="test-section">
        <h2>Test HTML Structure</h2>
        <p>This page simulates the Etsy listing structure to test seller extraction:</p>
        
        <!-- Simulate Etsy shop link structure -->
        <div id="etsy-shop-link">
            <a href="https://www.etsy.com/shop/BlackCatsMedia?ref=shop-header-name&listing_id=1633642699&from_page=listing">BlackCatsMedia</a>
        </div>
        
        <!-- Additional test structures -->
        <div class="shop-name">
            <a href="/shop/TestShop">TestShop</a>
        </div>
        
        <div class="seller-info">
            <a href="https://www.etsy.com/shop/AnotherTestShop">AnotherTestShop</a>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="loadScripts()">Load Extractor Scripts</button>
        <button onclick="testSelectors()">Test Selectors</button>
        <button onclick="testExtraction()">Test Full Extraction</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
        let scriptsLoaded = false;
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function loadScripts() {
            try {
                addResult('Loading CommonExtractor...', 'info');
                
                // Load CommonExtractor first
                const commonScript = document.createElement('script');
                commonScript.src = 'content-scripts/common-extractor.js';
                document.head.appendChild(commonScript);
                
                await new Promise(resolve => {
                    commonScript.onload = resolve;
                    commonScript.onerror = () => {
                        addResult('Failed to load CommonExtractor', 'error');
                        resolve();
                    };
                });
                
                // Wait a bit for CommonExtractor to initialize
                await new Promise(resolve => setTimeout(resolve, 500));
                
                addResult('Loading EtsyExtractor...', 'info');
                
                // Load EtsyExtractor
                const etsyScript = document.createElement('script');
                etsyScript.src = 'content-scripts/etsy-extractor.js';
                document.head.appendChild(etsyScript);
                
                await new Promise(resolve => {
                    etsyScript.onload = resolve;
                    etsyScript.onerror = () => {
                        addResult('Failed to load EtsyExtractor', 'error');
                        resolve();
                    };
                });
                
                // Wait a bit for EtsyExtractor to initialize
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (window.CommonExtractor && window.EtsyExtractor) {
                    addResult('Scripts loaded successfully!', 'success');
                    scriptsLoaded = true;
                } else {
                    addResult('Scripts loaded but objects not available', 'error');
                }
                
            } catch (error) {
                addResult(`Error loading scripts: ${error.message}`, 'error');
            }
        }
        
        function testSelectors() {
            if (!scriptsLoaded || !window.EtsyExtractor) {
                addResult('Please load scripts first', 'error');
                return;
            }
            
            addResult('Testing individual selectors...', 'info');
            
            const selectors = window.EtsyExtractor.selectors.seller;
            
            selectors.forEach((selector, index) => {
                try {
                    const element = document.querySelector(selector);
                    if (element) {
                        const text = element.textContent || element.innerText || '';
                        addResult(`Selector ${index + 1} (${selector}): Found "${text.trim()}"`, 'success');
                    } else {
                        addResult(`Selector ${index + 1} (${selector}): No match`, 'info');
                    }
                } catch (error) {
                    addResult(`Selector ${index + 1} (${selector}): Error - ${error.message}`, 'error');
                }
            });
        }
        
        function testExtraction() {
            if (!scriptsLoaded || !window.EtsyExtractor) {
                addResult('Please load scripts first', 'error');
                return;
            }
            
            addResult('Testing seller extraction...', 'info');
            
            try {
                const seller = window.EtsyExtractor.extractSeller();
                if (seller) {
                    addResult(`Extracted seller: "${seller}"`, 'success');
                    
                    // Test if it matches expected value
                    if (seller === 'BlackCatsMedia') {
                        addResult('✓ Correctly extracted "BlackCatsMedia"!', 'success');
                    } else {
                        addResult(`⚠ Expected "BlackCatsMedia" but got "${seller}"`, 'error');
                    }
                } else {
                    addResult('No seller extracted', 'error');
                }
            } catch (error) {
                addResult(`Error during extraction: ${error.message}`, 'error');
            }
        }
        
        // Auto-load scripts when page loads
        window.addEventListener('load', () => {
            addResult('Page loaded. Click "Load Extractor Scripts" to begin testing.', 'info');
        });
    </script>
</body>
</html>
